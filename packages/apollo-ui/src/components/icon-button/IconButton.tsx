import { cva } from "class-variance-authority"
import classNames from "classnames"

import { But<PERSON> } from "../button"
import styles from "./IconButton.module.css"
import type { IconButtonProps } from "./IconButtonProps"

const iconButtonVariants = cva(styles.iconButtonRoot, {
  variants: {
    size: {
      large: styles.iconButtonLarge,
      medium: styles.iconButtonMedium,
      small: styles.iconButtonSmall,
    },
  },
  defaultVariants: {
    size: "large",
  },
})

export function IconButton(props: IconButtonProps) {
  const { children, ref, size = "large", ...buttonProps } = props
  return (
    <Button
      variant="text"
      {...buttonProps}
      className={classNames(
        "ApolloIconButton-root",
        iconButtonVariants({
          size,
        }),
        buttonProps?.className
      )}
      ref={ref}
    >
      {children}
    </Button>
  )
}
