import { cva } from "class-variance-authority"
import classNames from "classnames"

import { IconButton } from "../icon-button"
import { Typography } from "../typography"
import styles from "./chip.module.css"
import type { ChipProps } from "./ChipProps"

const ChipVariants = cva(styles.chipRoot, {
  variants: {
    variant: {
      filled: styles.chipFilled,
      outline: styles.chipOutline,
    },
    size: {
      large: styles.chipLarge,
      medium: styles.chipMedium,
      small: styles.chipSmall,
    },
    color: {
      primary: styles.chipPrimary,
      negative: styles.chipNegative,
    },
  },
  defaultVariants: {
    variant: "filled",
    size: "medium",
    color: "primary",
  },
})

export function Chip({
  label,
  ref,
  onClose,
  onCheck,
  disabled,
  variant = "filled",
  size = "medium",
  color = "primary",
  className,
  ...divProps
}: ChipProps) {
  const ChipVariantStyles = ChipVariants({
    variant,
    size,
    color,
  })

  const handleCheck = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation()
    onCheck?.(event)
  }

  const handleClose = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation()
    onClose?.(event)
  }

  return (
    <div
      {...divProps}
      ref={ref}
      className={classNames(
        "ApolloChip-root",
        styles.chip,
        ChipVariantStyles,
        { [styles.chipDisabled]: disabled },
        className
      )}
    >
      {onCheck && (
        <IconButton
          className="ApolloChip-checkIcon"
          size={size}
          onClick={handleCheck}
          disabled={disabled}
          color={color}
        >
          <CheckIcon />
        </IconButton>
      )}
      <Typography level={size === "small" ? "bodySmall" : "bodyMedium"} color={color} className="ApolloChip-label">
        {label}
      </Typography>
      {onClose && (
        <IconButton
          className="ApolloChip-closeIcon"
          size={size}
          onClick={handleClose}
          disabled={disabled}
          color={color}
        >
          <CloseCircleIcon />
        </IconButton>
      )}
    </div>
  )
}

function CloseCircleIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="15"
      height="14"
      viewBox="0 0 15 14"
      fill="none"
    >
      <path
        d="M9.92421 4.94088C9.92421 4.88359 9.87734 4.83671 9.82005 4.83671L8.96067 4.84062L7.6664 6.38359L6.37343 4.84192L5.51276 4.83801C5.45546 4.83801 5.40859 4.88359 5.40859 4.94218C5.40859 4.96692 5.4177 4.99036 5.43333 5.00989L7.12734 7.02812L5.43333 9.04504C5.41759 9.06413 5.40887 9.08802 5.40859 9.11275C5.40859 9.17004 5.45546 9.21692 5.51276 9.21692L6.37343 9.21301L7.6664 7.67004L8.95937 9.21171L9.81875 9.21562C9.87604 9.21562 9.92291 9.17004 9.92291 9.11145C9.92291 9.08671 9.9138 9.06327 9.89817 9.04374L8.20677 7.02681L9.90078 5.00859C9.9164 4.99036 9.92421 4.96562 9.92421 4.94088Z"
        fill="currentColor"
      />
      <path
        d="M7.66658 1.16667C4.44523 1.16667 1.83325 3.77865 1.83325 7C1.83325 10.2214 4.44523 12.8333 7.66658 12.8333C10.8879 12.8333 13.4999 10.2214 13.4999 7C13.4999 3.77865 10.8879 1.16667 7.66658 1.16667ZM7.66658 11.8438C4.99211 11.8438 2.82284 9.67448 2.82284 7C2.82284 4.32552 4.99211 2.15625 7.66658 2.15625C10.3411 2.15625 12.5103 4.32552 12.5103 7C12.5103 9.67448 10.3411 11.8438 7.66658 11.8438Z"
        fill="currentColor"
      />
    </svg>
  )
}

function CheckIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="15"
      height="14"
      viewBox="0 0 15 14"
      fill="none"
    >
      <path
        d="M13.3853 2.33333H12.386C12.2459 2.33333 12.1129 2.39857 12.0271 2.51019L6.13254 10.0815L3.30606 6.45023C3.2633 6.39519 3.2088 6.35068 3.14665 6.32005C3.0845 6.28942 3.01631 6.27347 2.94721 6.27338H1.94786C1.85207 6.27338 1.79917 6.385 1.85779 6.46038L5.77369 11.4905C5.95669 11.7254 6.30839 11.7254 6.49282 11.4905L13.4754 2.51888C13.534 2.44495 13.4811 2.33333 13.3853 2.33333Z"
        fill="currentColor"
      />
    </svg>
  )
}
