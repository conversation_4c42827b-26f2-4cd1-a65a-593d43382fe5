@layer legacy {
    .autocomplete {
        --apl-autocomplete-gap: var(--apl-space-gap-2xs);
        --apl-autocomplete-chip-spacing: var(--apl-space-gap-2xs);
        /* autocompleteInputWrapper */
        --apl-autocomplete-input-wrapper-scrollbar-color: var(--apl-colors-border-tertiary) transparent;
        --apl-autocomplete-input-wrapper-background-color: var(--apl-colors-border-tertiary);
        /* autocompletePopoverRoot */
        --apl-autocomplete-popover-color: var(--apl-colors-content-default);
        --apl-autocomplete-popover-gap: var(--apl-space-gap-xs);
        /* autocompleteMenuRoot  */
        --apl-autocomplete-menu-scrollbar-color: var(--apl-colors-border-tertiary) transparent;
        --apl-autocomplete-menu-background: var(--apl-colors-surface-static-ui-default, #FFF);
        --apl-autocomplete-menu-box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        /* autocompleteMenuItem */
        --apl-autocomplete-menu-item-padding: var(--apl-space-padding-xs, 8px);
        --apl-autocomplete-menu-item-gap: var(--apl-space-gap-xs, 8px);
        --apl-autocomplete-menu-item-background: var(--apl-colors-surface-static-default1, #FFF);
        --apl-autocomplete-menu-item-hover-background: var(--apl-colors-surface-static-ui-hover, #F6F7FB);
    }

    .menuOption {
        padding-left: var(--menu-option-indent, 8px) !important;
    }

    .menuOptionIndented {
        --menu-option-indent: var(--apl-space-gap-xl);
        --menu-option-margin-left-right: 0px;
        --menu-option-margin-top-bottom: 0px;
        --menu-option-indent-border-radius: 0px
    }

    .small {
        --apl-autocomplete-height: 32px;
        --apl-autocomplete-chip-height: 24px;
        --apl-autocomplete-padding: var(--apl-space-padding-xs) var(--apl-space-padding-md);
    }

    .medium {
        --apl-autocomplete-height: 42px;
        --apl-autocomplete-chip-height: 32px;
        --apl-autocomplete-padding: var(--apl-space-padding-xs) var(--apl-space-padding-md);
    }

}

@layer apollo {
    .autocomplete {
        --apl-autocomplete-gap: var(--apl-alias-spacing-gap-gap4);
        --apl-autocomplete-chip-spacing: var(--apl-alias-spacing-gap-gap5);
        /* autocompleteInputWrapper */

        /* autocompletePopoverRoot */
        --apl-autocomplete-popover-color: var(--apl-alias-color-background-and-surface-on-surface);
        --apl-autocomplete-popover-gap: var(--apl-alias-spacing-gap-gap3);
        /* autocompleteMenuRoot  */
        --apl-autocomplete-menu-scrollbar-color: var(--apl-alias-color-background-and-surface-on-background, #F8F7F7) transparent;
        --apl-autocomplete-menu-background: var(--apl-alias-color-background-and-surface-background, #FFF);
        --apl-autocomplete-menu-box-shadow: 4px 4px 15px 3px rgba(0, 0, 0, 0.05);
        /* autocompleteMenuItem */
        --apl-autocomplete-menu-item-padding: var(--apl-alias-spacing-padding-padding5, 8px);
        --apl-autocomplete-menu-item-gap: var(--apl-alias-spacing-gap-gap5, 8px);
        --apl-autocomplete-menu-item-background: var(--apl-alias-color-background-and-surface-background);
        --apl-autocomplete-menu-item-hover-background: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
        /* icon */
        --apl-autocomplete-icon-button-size: 20px;
        --apl-autocomplete-icon-button-padding: 4px 1px;


    }

    .menuOption {
        width: auto !important;
        padding-left: var(--menu-option-indent, 8px) !important;
        margin: var(--menu-option-margin-top-bottom) var(--menu-option-margin-left-right) !important;
        border-radius: var(--menu-option-indent-border-radius) !important;
    }

    .menuOptionShowCheckbox {
        --menu-option-checkbox-background-color: transparent;

        &:hover,
        &[data-highlighted] {
            background-color: var(--menu-option-checkbox-background-color) !important;
        }

        &[data-checked],
        &[data-selected] {
            background-color: var(--menu-option-checkbox-background-color) !important;

            &:hover {
                background-color: var(--menu-option-checkbox-background-color) !important;
            }

        }
    }

    .menuOptionIndented {
        --menu-option-indent: var(--apl-alias-spacing-gap-gap5, 8px);
        --menu-option-margin-top-bottom: var(--apl-alias-spacing-gap-gap2, 2px);
        --menu-option-margin-left-right: var(--apl-alias-spacing-gap-gap8, 16px);
        --menu-option-indent-border-radius: var(--apl-alias-radius-radius2, 4px);
    }

    .small {
        --apl-autocomplete-height: 32px;
        --apl-autocomplete-chip-height: 24px;
        --apl-autocomplete-padding: var(--apl-alias-spacing-padding-padding3, 4px) var(--apl-alias-spacing-padding-padding5, 8px);
    }

    .medium {
        --apl-autocomplete-height: 42px;
        --apl-autocomplete-chip-height: 28px;
        --apl-autocomplete-padding: var(--apl-alias-spacing-padding-padding4, 6px) var(--apl-alias-spacing-padding-padding5, 8px);
    }

    .menuOptionMenuLabelText {
        padding: var(--apl-autocomplete-menu-item-padding);
        width: 100%;
    }
}

.autocompleteRoot {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    max-width: 100%;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    /* Safari/Chrome, other WebKit */
    -moz-box-sizing: border-box;
    /* Firefox, other Gecko */


    min-width: 128px;
    min-height: var(--apl-autocomplete-height, 42px);
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    max-height: -webkit-fit-content;
    max-height: -moz-fit-content;
    max-height: fit-content;

    padding: var(--apl-autocomplete-padding);
    gap: var(--apl-autocomplete-gap);


    & :global(.ApolloInput-endDecorator) {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        gap: var(--apl-autocomplete-gap);

        & :global(.ApolloIconButton-root) {
            --apl-icon-button-size: var(--apl-autocomplete-icon-button-size, inherit);
            --apl-icon-button-padding: var(--apl-autocomplete-icon-button-padding, inherit);
            color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        }
    }

    & input {
        display: inline-flex;
        padding: 0;
        flex: 1;
        min-width: 100px;
    }

    &:global(.thumbnail) {
        height: var(--apl-autocomplete-height, 42px);

        &:global(.multiple) {
            & input {
                width: 0;
                min-width: 0;
            }
        }

        & :global(.ApolloAutocomplete-inputWrapper) {
            overflow: hidden;
            text-overflow: ellipsis;
            /* Added for text truncation */
            white-space: nowrap;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-direction: row;
            flex-wrap: nowrap;
        }

        & :global(.ApolloAutocomplete-clearButton) {
            opacity: 0;
            transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        &:hover {
            & :global(.ApolloAutocomplete-clearButton) {
                opacity: 1;
            }
        }
    }

    min-height: var(--apl-autocomplete-height);
}

.autocompleteRootDefault {
    height: var(--apl-input-height);
    flex-wrap: nowrap;
    overflow: hidden;
}

.autocompleteInputWrapper {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    overflow: hidden;
    gap: var(--apl-autocomplete-gap);

    max-height: 256px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    /* Smooth scrolling on iOS */
    scrollbar-width: thin;
    /* Firefox scrollbar */
    scrollbar-color: var(--apl-autocomplete-input-wrapper-scrollbar-color);
    /* Firefox scrollbar colors */

    /* Webkit scrollbar styles */
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background-color: var(--apl-autocomplete-input-wrapper-background-color);
        border-radius: 3px;
    }
}

.autocompletePopoverRoot {
    width: var(--anchor-width);
    color: var(--apl-autocomplete-popover-color);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: var(--apl-autocomplete-popover-gap);
    outline: none;

    &[data-side="top"] {
        flex-direction: column-reverse;
    }

    & :global(.ApolloAutocomplete-chevronIcon) {
        transition: transform 200ms cubic-bezier(0.4, 0, 0.2, 1);
    }

    &[data-open] {
        & :global(.ApolloAutocomplete-chevronIcon) {
            transform: rotate(180deg);
        }
    }
}

.autocompleteMenuRoot {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    max-height: 265px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--apl-autocomplete-menu-scrollbar-color);
    border-radius: 8px;
    background: var(--apl-autocomplete-menu-background);
    box-shadow: var(--apl-autocomplete-menu-box-shadow);
    -webkit-box-shadow: var(--apl-autocomplete-menu-box-shadow);
    width: 100%;
    composes: apl-typography-body-large from '../../base.module.css';

    &>* {
        flex-shrink: 0;
    }

    & :global(.ApolloMenuItem-root) {

        &:first-of-type,
        &:last-of-type {
            border-radius: 8px;
        }

        & :global(.ApolloTypography--root) {
            font-size: inherit;
        }

    }
}

.autocompleteMenuItem {
    display: flex;
    padding: var(--apl-autocomplete-menu-item-padding);
    align-items: center;
    gap: var(--apl-autocomplete-menu-item-gap);
    align-self: stretch;
    background: var(--apl-autocomplete-menu-item-background);

    &:hover {
        background: var(--apl-autocomplete-menu-item-hover-background);
    }
}

.autocompleteTriggerWrapper {
    width: fit-content;
    max-width: 100%;

    &:global(.open) {
        & :global(.ApolloInput-controlRoot) {
            opacity: 0;
        }
    }
}

.autocompleteFullWidth {
    width: 100%;
}

.popoverMenuList {
    background-color: white;
}

.chipContainer {
    display: inline-flex;
    flex-wrap: nowrap;
    gap: var(--apl-autocomplete-chip-spacing, 8px);
    overflow: hidden;
    align-items: center;
    max-width: 100%;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
}

.autocompleteFullTags {
    gap: var(--apl-autocomplete-chip-spacing, 8px);
}

.truncatedChip {
    max-width: var(--apl-autocomplete-chip-min-width, 30%);
    flex: 0 1 auto;
}

.autocompleteFullWidth {
    width: 100%;
}

.autocompleteScrollTrigger {
    display: flex;
    padding: 4px 24px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    width: 100%;
}

.loadingText{
    color: var(--apl-alias-color-outline-and-border-outline, #ADABAB);
}
