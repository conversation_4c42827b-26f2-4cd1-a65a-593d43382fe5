import {render, screen} from '@testing-library/react'
import But<PERSON>, { buttonVariants } from "./Button"
import { mergeClass } from '@/utils/helpers'

describe("<Button />", () => {
  test('loads and displays button with primary color (default)', async () => {
    const BtnMsg = "Test Button"
    const BtnOutlineMsg = "Test Button Outline"
    const BtnPlainMsg = "Test Button Plain"
    render(
      <>
        <Button data-testid="test-button-default">{BtnMsg}</Button>
        <Button data-testid="test-button-outline" variant="outline" >{BtnOutlineMsg}</Button>
        <Button data-testid="test-button-text" variant="text" >{BtnPlainMsg}</Button>
      </>
    )

    const btn = screen.getByTestId('test-button-default')
    const btnOutline = screen.getByTestId('test-button-outline')
    const btnText = screen.getByTestId('test-button-text')

    expect(btn.textContent).toBe(BtnMsg)
    expect(btnOutline.textContent).toBe(BtnOutlineMsg)
    expect(btnText.textContent).toBe(BtnPlainMsg)

    // default (filled)
    expect(btn).toHaveClass(mergeClass(buttonVariants({ })))
    expect(btn).toHaveClass(mergeClass(buttonVariants({ color: "primary" })))
    expect(btn).toHaveClass(mergeClass(buttonVariants({ variant: "solid" })))
    expect(btn).toHaveClass(mergeClass(buttonVariants({ variant: "solid", color: "primary" })))

    // outline
    expect(btnOutline).toHaveClass(mergeClass(buttonVariants({ variant: "outline" })))
    expect(btnOutline).toHaveClass(mergeClass(buttonVariants({ variant: "outline", color: "primary" })))

    // text
    expect(btnText).toHaveClass(mergeClass(buttonVariants({ variant: "plain" })))
    expect(btnText).toHaveClass(mergeClass(buttonVariants({ variant: "plain", color: "primary" })))
  })

  test('loads and displays button with danger color', async () => {
    const BtnMsg = "Test Button"
    const BtnOutlineMsg = "Test Button Outline"
    const BtnPlainMsg = "Test Button Plain"
    render(
      <>
        <Button color="danger" data-testid="test-button-default">{BtnMsg}</Button>
        <Button color="danger" data-testid="test-button-outline" variant="outline">{BtnOutlineMsg}</Button>
        <Button color="danger" data-testid="test-button-text" variant="text">{BtnPlainMsg}</Button>
      </>
    )

    const btn = screen.getByTestId('test-button-default')
    const btnOutline = screen.getByTestId('test-button-outline')
    const btnText = screen.getByTestId('test-button-text')

    expect(btn.textContent).toBe(BtnMsg)
    expect(btnOutline.textContent).toBe(BtnOutlineMsg)
    expect(btnText.textContent).toBe(BtnPlainMsg)

    // default (filled)
    expect(btn).toHaveClass(mergeClass(buttonVariants({ color: "danger" })))
    expect(btn).toHaveClass(mergeClass(buttonVariants({ variant: "solid", color: "danger" })))

    // outline
    expect(btnOutline).toHaveClass(mergeClass(buttonVariants({ variant: "outline", color: "danger" })))

    // text
    expect(btnText).toHaveClass(mergeClass(buttonVariants({ variant: "plain", color: "danger" })))
  })
})
