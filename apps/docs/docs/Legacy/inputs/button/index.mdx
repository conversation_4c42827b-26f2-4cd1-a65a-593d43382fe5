---
title: Button
id: legacy:component:button
slug: /legacy/components/button
---

import { But<PERSON> } from "@design-systems/apollo-ui"

```tsx
import { But<PERSON> } from "@design-systems/apollo-ui"
```

The `Button` component is used to displays a button or a component that looks like a button.

```jsx live
function ButtonDemo() {
  return (
    <Button variant="filled">Button</Button>
  )
}
```

## Props

> This component extends the type from `ButtonProps` which is from [@mui/base](https://mui.com/base-ui/react-button).

| Name                   | Type                                           | Required | Default                | Description                                                                                    |
| ---------------------- | ---------------------------------------------- | -------- | ---------------------- | ---------------------------------------------------------------------------------------------- |
| **`color`**            | `'danger' \| 'primary'`                        |          | `primary`              | The color of the component. It supports those theme colors that make sense for this component. |
| **`disabled`**         | `bool`                                         |          | `false`                | If `true`, the component is disabled.                                                          |
| **`endDecorator`**     | `node`                                         |          | -                      | Element placed after the children.                                                             |
| **`fullWidth`**        | `bool`                                         |          | `false`                | If `true`, the button will take up the full width of its container.                            |
| **`loading`**          | `bool`                                         |          | `false`                | If `true`, the loading indicator is shown and the button becomes disabled.                     |
| **`loadingIndicator`** | `node`                                         |          | `<CircularProgress />` | The indicator shown while loading.                                                             |
| **`loadingPosition`**  | `'start' \| 'end'`                             |          | `start`                | The position of the loading indicator within the button.                                       |
| **`size`**             | `'sm' \| 'md' \| 'lg'`                         |          | `lg`                   | The size of the component.                                                                     |
| **`startDecorator`**   | `node`                                         |          | -                      | Element placed before the children.                                                            |
| **`variant`**          | `'outline' \| 'plain' \| 'solid'`              |          | `solid`                | The variant of the button.                                                                     |
| **`action`**           | `func \| { current?: { focusVisible: func } }` |          | -                      | A ref for imperative actions. Supports `focusVisible()` action.                                |
| **`as`**               | `elementType`                                  |          | {}                     | The component used for the root node. Can be an HTML element or a React component.             |

## Examples

### Solid (Default)

The default button style with a solid background.

```jsx live
function ButtonDemo() {
  return (
    <div style={{ display: "flex", gap: "16px" }}>
      <Button>Button</Button>
      <Button color="danger">Button</Button>
    </div>
  )
}
```

### Outline

A button style with an outline border.

```jsx live
function ButtonDemo() {
  return (
    <div style={{ display: "flex", gap: "16px" }}>
      <Button variant="outline">Button</Button>
      <Button variant="outline" color="danger">
        Button
      </Button>
    </div>
  )
}
```

### Plain

A button style with no background or border, just text.

```jsx live
function MyComponent() {
  return (
    <div style={{ display: "flex", gap: "16px" }}>
      <Button variant="plain">Button</Button>
      <Button variant="plain" color="danger">
        Button
      </Button>
    </div>
  )
}
```

### Disabled

A button that is disabled and not interactive.

```jsx live
function ButtonDemo() {
  return (
    <div style={{ display: "flex", gap: "16px" }}>
      <Button disabled>solid (default)</Button>
      <Button variant="outline" disabled>
        outline
      </Button>
      <Button variant="plain" disabled>
        plain
      </Button>
    </div>
  )
}
```

### With `StartDecorator` and `EndDecorator`

A button with icons or elements before and after the button text.

```jsx live
function ButtonDemo() {
  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
      <div style={{ display: "flex", gap: "16px" }}>
        <Button
          startDecorator={
            <ChevronLeft
              style={{ height: "16px", width: "16px", marginRight: "4px" }}
            />
          }
        >
          Button
        </Button>
        <Button
          variant="outline"
          startDecorator={
            <ChevronLeft
              style={{ height: "16px", width: "16px", marginRight: "4px" }}
            />
          }
        >
          Button
        </Button>
        <Button
          variant="plain"
          startDecorator={
            <ChevronLeft
              style={{ height: "16px", width: "16px", marginRight: "4px" }}
            />
          }
        >
          Button
        </Button>
      </div>
      <div style={{ display: "flex", gap: "16px" }}>
        <Button
          endDecorator={
            <ChevronRight
              style={{ height: "16px", width: "16px", marginLeftt: "4px" }}
            />
          }
        >
          Button
        </Button>
        <Button
          variant="outline"
          endDecorator={
            <ChevronRight
              style={{ height: "16px", width: "16px", marginLeftt: "4px" }}
            />
          }
        >
          Button
        </Button>
        <Button
          variant="plain"
          endDecorator={
            <ChevronRight
              style={{ height: "16px", width: "16px", marginLeftt: "4px" }}
            />
          }
        >
          Button
        </Button>
      </div>
    </div>
  )
}
```

### With `Loading`

A button that shows a loading indicator and becomes disabled.

```jsx live
function ButtonDemo() {
  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
      <Button loading> default </Button>
      <Button
        loading
        loadingIndicator={
          <RefreshCw
            style={{
              height: "16px",
              width: "16px",
              marginRight: "4px",
              animation: "spin 1s linear infinite",
            }}
          />
        }
      >
        {" "}
        with custom loading{" "}
      </Button>
      <Button
        variant="plain"
        loading
        loadingIndicator={
          <RefreshCw
            style={{
              height: "16px",
              width: "16px",
              marginRight: "4px",
              animation: "spin 1s linear infinite",
            }}
          />
        }
      >
        {" "}
        plain with custom loading{" "}
      </Button>
      <Button
        variant="outline"
        loading
        loadingIndicator={
          <RefreshCw
            style={{
              height: "16px",
              width: "16px",
              marginRight: "4px",
              animation: "spin 1s linear infinite",
            }}
          />
        }
      >
        {" "}
        outline with custom loading{" "}
      </Button>
    </div>
  )
}
```

### Using Other Element Than Button with `As`

A button that uses a different HTML element or component for its root.

```jsx live
function ButtonDemo() {
  return (
    <Button as="a" href="#">
      {" "}
      This is &lt;a/&gt; not &lt;button/&gt;
    </Button>
  )
}
```

### Overriding Default Style

Custom styles applied to the button, using either `className` with Tailwind or inline styles.

```jsx live
function ButtonDemo() {
  return (
    <div style={{ display: "flex", gap: "16px" }}>
      <Button
        style={{
          backgroundColor: "black",
          color: "white",
          "&:hover": { backgroundColor: "gray" },
          marginTop: "8px",
        }}
      >
        {" "}
        Button custom className
      </Button>
      <Button style={{ backgroundColor: "red" }}> Button inline</Button>
    </div>
  )
}
```
