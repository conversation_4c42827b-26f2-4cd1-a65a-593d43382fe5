import { <PERSON><PERSON> } from "@apollo/ui"

import "@apollo/ui/style.css"

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: "@apollo∕ui/Components/Inputs/Button",
  component: Button,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: "centered",
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["solid", "outline", "plain"],
      description: "The visual style variant of the button",
    },
    size: {
      control: { type: "select" },
      options: ["medium", "small"],
      description: "The size of the button",
    },
    color: {
      control: { type: "select" },
      options: ["primary", "danger"],
      description: "The color theme of the button",
    },
    fullWidth: {
      control: { type: "boolean" },
      description:
        "Whether the button should take the full width of its container",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the button is disabled",
    },
    children: {
      control: { type: "text" },
      description: "The content of the button",
    },
    startDecorator: {
      control: false,
      description: "Element to display at the start of the button",
    },
    endDecorator: {
      control: false,
      description: "Element to display at the end of the button",
    },
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {
    onClick: () => {},
    children: "Button",
  },
} satisfies Meta<typeof Button>

export default meta
type Story = StoryObj<typeof Button>

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default: Story = {
  args: {
    children: "Default Button",
    fullWidth: true,
  },
}

export const Primary: Story = {
  args: {
    variant: "filled",
    color: "primary",
    children: "Primary Button",
  },
}

export const Negative: Story = {
  args: {
    variant: "filled",
    color: "negative",
    children: "Negative Button",
  },
}

export const Outline: Story = {
  args: {
    variant: "outline",
    color: "primary",
    children: "Outline Button",
  },
}

export const OutlineNegative: Story = {
  args: {
    variant: "outline",
    color: "negative",
    children: "Outline Negative",
  },
}

export const Text: Story = {
  args: {
    variant: "text",
    color: "primary",
    children: "Text Button",
  },
}

export const TextNegative: Story = {
  args: {
    variant: "text",
    color: "negative",
    children: "Text Negative",
  },
}

export const Small: Story = {
  args: {
    size: "small",
    children: "Small Button",
  },
}

export const Medium: Story = {
  args: {
    size: "medium",
    children: "Medium Button",
  },
}

export const FullWidth: Story = {
  args: {
    fullWidth: true,
    children: "Full Width Button",
  },
  parameters: {
    layout: "padded",
  },
}

export const Disabled: Story = {
  args: {
    disabled: true,
    children: "Disabled Button",
  },
}

export const WithDecorators: Story = {
  args: {
    children: "Button with Icons",
    startDecorator: "🚀",
    endDecorator: "✨",
  },
}

export const AsLink: Story = {
  args: {
    href: "https://example.com",
    children: "Link Button",
    target: "_blank",
    rel: "noopener noreferrer",
  },
}

// Showcase all variants in a grid
export const AllVariants: Story = {
  render: () => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(3, 1fr)",
        gap: "16px",
        alignItems: "center",
      }}
    >
      <Button variant="filled" color="primary">
        Filled Primary
      </Button>
      <Button variant="outline" color="primary">
        Outline Primary
      </Button>
      <Button variant="text" color="primary">
        Text Primary
      </Button>

      <Button variant="filled" color="negative">
        Filled Negative
      </Button>
      <Button variant="outline" color="negative">
        Outline Negative
      </Button>
      <Button variant="text" color="negative">
        Text Negative
      </Button>

      <Button variant="filled" size="small">
        Small Filled
      </Button>
      <Button variant="outline" size="small">
        Small Outline
      </Button>
      <Button variant="text" size="small">
        Small Text
      </Button>
    </div>
  ),
  parameters: {
    layout: "centered",
  },
}
