import { Chip } from "@apollo/ui"

import { ComponentBox, ComponentGroup } from "./common"

export function Chips() {
  return (
    <ComponentGroup>
      <ComponentBox>
        <h2 className="text-xl font-semibold">Chip</h2>
        <Chip label="Category: All" />
        <Chip
          label="Category: All"
          onClose={() => {
            console.log("[DEBUG] Close!")
          }}
        />
        <Chip disabled label="Tag" />
        <Chip
          disabled
          onClose={() => {
            console.log("[DEBUG] Close!")
          }}
          label="Tag"
          onCheck={() => {
            console.log("[DEBUG] check event")
          }}
        />
        <Chip size="small" label="Tag" />
        <Chip
          size="large"
          label="Tag Outline"
          onClose={() => {
            console.log("[DEBUG] Close!")
          }}
          variant="outline"
        />
         <Chip
          size="large"
          label="Tag Outline"
          disabled
          onClose={() => {
            console.log("[DEBUG] Close!")
          }}
          variant="outline"
        />
        <Chip
          label="LongggggggLongggggggLongggggggLonggggggg"
          style={{ width: "100px" }}
        />
        <Chip
          label="Tag"
          onClose={() => {
            console.log("[DEBUG] close event")
          }}
          onCheck={() => {
            console.log("[DEBUG] check event")
          }}
        />
        <Chip
          label="Tag"
          onClose={() => {
            console.log("[DEBUG] close event")
          }}
          onCheck={() => {
            console.log("[DEBUG] check event")
          }}
          color="negative"
        />
        <Chip
          label="Tag"
          onClose={() => {
            console.log("[DEBUG] close event")
          }}
          onCheck={() => {
            console.log("[DEBUG] check event")
          }}
          variant="outline"
          color="negative"
          onClick={() => {
            console.log("[DEBUG] click event")
          }}
        />
      </ComponentBox>
    </ComponentGroup>
  )
}
