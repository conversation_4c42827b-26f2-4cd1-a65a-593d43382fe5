---
title: Button
description: Displays a button or a component that looks like a button.
featured: true
component: true
links:
  api: https://gitlab.cjexpress.io/cjexpress/design-systems/apollo/-/blob/main/packages/ui/src/components/Button/ButtonProps.ts
  figma: https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=308-4359&m=dev
---

<ComponentPreviewUI name="button-preview" />

## Usage

```tsx
import { But<PERSON> } from "@design-systems/apollo-ui"
```

```tsx
<Button variant="filled">Button</Button>
```

# Props

> This component extends the type from `ButtonProps` which is from [@mui/base](https://mui.com/base-ui/react-button).

| Name                   | Type                                           | Required | Default                | Description                                                                                    |
| ---------------------- | ---------------------------------------------- | -------- | ---------------------- | ---------------------------------------------------------------------------------------------- |
| **`color`**            | `'danger' \| 'primary'`                        |          | `primary`              | The color of the component. It supports those theme colors that make sense for this component. |
| **`disabled`**         | `bool`                                         |          | `false`                | If `true`, the component is disabled.                                                          |
| **`endDecorator`**     | `node`                                         |          | -                      | Element placed after the children.                                                             |
| **`fullWidth`**        | `bool`                                         |          | `false`                | If `true`, the button will take up the full width of its container.                            |
| **`loading`**          | `bool`                                         |          | `false`                | If `true`, the loading indicator is shown and the button becomes disabled.                     |
| **`loadingIndicator`** | `node`                                         |          | `<CircularProgress />` | The indicator shown while loading.                                                             |
| **`loadingPosition`**  | `'start' \| 'end'`                             |          | `start`                | The position of the loading indicator within the button.                                       |
| **`size`**             | `'md' \| 'lg'`                                 |          | `md`                   | The size of the component.                                                                     |
| **`startDecorator`**   | `node`                                         |          | -                      | Element placed before the children.                                                            |
| **`variant`**          | `'outline' \| 'plain' \| 'solid'`              |          | `solid`                | The variant of the button.                                                                     |
| **`action`**           | `func \| { current?: { focusVisible: func } }` |          | -                      | A ref for imperative actions. Supports `focusVisible()` action.                                |
| **`as`**               | `elementType`                                  |          | {}                     | The component used for the root node. Can be an HTML element or a React component.             |

## Examples

### Solid (Default)

The default button style with a solid background.

<ComponentPreviewUI name="button-demo" />

### Outline

A button style with an outline border.

<ComponentPreviewUI name="button-outline" />

### Plain

A button style with no background or border, just text.

<ComponentPreviewUI name="button-plain" />

### Disabled

A button that is disabled and not interactive.

<ComponentPreviewUI name="button-disabled" />

### With `StartDecorator` and `EndDecorator`

A button with icons or elements before and after the button text.

<ComponentPreviewUI name="button-icon" />

### With `Loading`

A button that shows a loading indicator and becomes disabled.

<ComponentPreviewUI name="button-loading" />

### Using Other Element Than Button with `As`

A button that uses a different HTML element or component for its root.

<ComponentPreviewUI name="button-as" />

### Overriding Default Style

Custom styles applied to the button, using either `className` with Tailwind or inline styles.

<ComponentPreviewUI name="button-override" />
